/* Advanced Task Manager Styles */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap');

/* CSS Variables for Theme Support */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --danger-color: #f44336;
    --info-color: #2196F3;

    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;

    --border-color: #e2e8f0;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.15);

    --border-radius: 12px;
    --border-radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    --border-color: #334155;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--text-primary);
    min-height: 100vh;
    padding: 20px;
    transition: var(--transition);
    position: relative;
    overflow-x: hidden;
}

/* Background Animation */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('./imge/wallpaperflare.com_wallpaper\ \(2\).jpg') no-repeat center center/cover;
    opacity: 0.1;
    z-index: -2;
}

body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
            rgba(102, 126, 234, 0.1) 0%,
            rgba(118, 75, 162, 0.1) 25%,
            rgba(240, 147, 251, 0.1) 50%,
            rgba(102, 126, 234, 0.1) 75%,
            rgba(118, 75, 162, 0.1) 100%);
    animation: gradientShift 15s ease infinite;
    z-index: -1;
}

@keyframes gradientShift {

    0%,
    100% {
        opacity: 0.1;
    }

    50% {
        opacity: 0.2;
    }
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-medium);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-heavy);
}

/* Main Container */
.container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 100vh;
    padding: 20px;
}

.todo-app {
    width: 100%;
    max-width: 800px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-heavy);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    overflow: hidden;
    animation-duration: 0.6s;
}

/* Header Section */
.app-header {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    position: relative;
}

.app-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
}

.app-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.app-header h1 i {
    font-size: 2rem;
}

.app-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Statistics Dashboard */
.stats-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    padding: 2rem;
    background: var(--bg-tertiary);
}

.stat-card {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: var(--info-color);
}

.stat-card:nth-child(2) .stat-icon {
    background: var(--success-color);
}

.stat-card:nth-child(3) .stat-icon {
    background: var(--warning-color);
}

.stat-card:nth-child(4) .stat-icon {
    background: var(--primary-color);
}

.stat-info h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Progress Section */
.progress-section {
    padding: 2rem;
    background: var(--bg-secondary);
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.progress-info h3 {
    color: var(--text-primary);
    font-size: 1.2rem;
}

.progress-info span {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.progress-bar-container {
    width: 100%;
    height: 12px;
    background: var(--bg-tertiary);
    border-radius: 6px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 6px;
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

/* Task Input Section */
.task-input-section {
    padding: 2rem;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

.task-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.input-group input,
.input-group textarea {
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition);
    resize: vertical;
}

.input-group input:focus,
.input-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.task-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.option-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.option-group select,
.option-group input[type="datetime-local"] {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-family: inherit;
    transition: var(--transition);
}

.option-group select:focus,
.option-group input[type="datetime-local"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.add-task-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-medium);
}

.add-task-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.add-task-btn:active {
    transform: translateY(0);
}

/* Filter and Search Section */
.filter-section {
    padding: 2rem;
    background: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    justify-content: space-between;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-family: inherit;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.sort-options select {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-family: inherit;
    cursor: pointer;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-muted);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.empty-state p {
    font-size: 1rem;
}

/* Tasks Container */
.tasks-container {
    padding: 2rem;
    background: var(--bg-secondary);
    max-height: 600px;
    overflow-y: auto;
}

.task-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Individual Task Item */
.task-item {
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.task-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.task-item.completed {
    opacity: 0.7;
    background: var(--bg-secondary);
}

.task-item.overdue {
    border-left: 4px solid var(--danger-color);
}

.task-item.high-priority {
    border-left: 4px solid var(--danger-color);
}

.task-item.medium-priority {
    border-left: 4px solid var(--warning-color);
}

.task-item.low-priority {
    border-left: 4px solid var(--success-color);
}

.task-item.urgent-priority {
    border-left: 4px solid var(--danger-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {

    0%,
    100% {
        box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.4);
    }

    50% {
        box-shadow: 0 0 0 10px rgba(244, 67, 54, 0);
    }
}

.task-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.task-checkbox {
    width: 24px;
    height: 24px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-secondary);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-top: 2px;
}

.task-checkbox:hover {
    border-color: var(--primary-color);
}

.task-checkbox.checked {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.task-checkbox.checked::before {
    content: '✓';
    font-size: 14px;
    font-weight: bold;
}

.task-content {
    flex: 1;
    min-width: 0;
}

.task-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    word-wrap: break-word;
}

.task-item.completed .task-title {
    text-decoration: line-through;
    color: var(--text-muted);
}

.task-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1rem;
}

.task-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.task-tag {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.task-tag.category {
    background: var(--info-color);
    color: white;
}

.task-tag.priority {
    background: var(--warning-color);
    color: white;
}

.task-tag.due-date {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.task-tag.overdue {
    background: var(--danger-color);
    color: white;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.task-action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.task-action-btn:hover {
    transform: scale(1.1);
}

.task-action-btn.edit {
    background: var(--warning-color);
    color: white;
}

.task-action-btn.delete {
    background: var(--danger-color);
    color: white;
}

/* Action Buttons */
.action-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    z-index: 1000;
}

.action-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-medium);
    background: var(--primary-color);
    color: white;
}

.action-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-heavy);
}

.action-btn.danger {
    background: var(--danger-color);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-heavy);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: var(--text-primary);
    font-size: 1.3rem;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted);
    transition: var(--transition);
}

.close-modal:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.detail-group {
    margin-bottom: 1.5rem;
}

.detail-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.detail-group input,
.detail-group textarea,
.detail-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-family: inherit;
    transition: var(--transition);
}

.detail-group input:focus,
.detail-group textarea:focus,
.detail-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-primary,
.btn-secondary {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--secondary-color);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--border-color);
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.notification {
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 500;
    box-shadow: var(--shadow-medium);
    animation: notificationSlideIn 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 300px;
}

@keyframes notificationSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.notification.success {
    background: var(--success-color);
}

.notification.error {
    background: var(--danger-color);
}

.notification.warning {
    background: var(--warning-color);
}

.notification.info {
    background: var(--info-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .todo-app {
        max-width: 100%;
    }

    .app-header h1 {
        font-size: 2rem;
    }

    .stats-dashboard {
        grid-template-columns: repeat(2, 1fr);
        padding: 1rem;
    }

    .task-options {
        grid-template-columns: 1fr;
    }

    .filter-section {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .filter-buttons {
        justify-content: center;
    }

    .action-buttons {
        bottom: 10px;
        right: 10px;
    }

    .theme-toggle {
        top: 10px;
        right: 10px;
    }
}

@media (max-width: 480px) {
    .stats-dashboard {
        grid-template-columns: 1fr;
    }

    .task-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .task-actions {
        justify-content: flex-start;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }
}